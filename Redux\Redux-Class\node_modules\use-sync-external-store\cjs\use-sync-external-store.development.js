/**
 * @license React
 * use-sync-external-store.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

"use strict";
if ("production" !== process.env.NODE_ENV) {
  "undefined" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&
    "function" ===
      typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&
    __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());
  var useSyncExternalStore$jscomp$inline_1 =
    require("react").useSyncExternalStore;
  console.error(
    "The main 'use-sync-external-store' entry point is not supported; all it does is re-export useSyncExternalStore from the 'react' package, so it only works with React 18+.\n\nIf you wish to support React 16 and 17, import from 'use-sync-external-store/shim' instead. It will fall back to a shimmed implementation when the native one is not available.\n\nIf you only support React 18+, you can import directly from 'react'."
  );
  exports.useSyncExternalStore = useSyncExternalStore$jscomp$inline_1;
  "undefined" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&
    "function" ===
      typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&
    __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());
}
