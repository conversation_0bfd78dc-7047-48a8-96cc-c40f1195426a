import { useState } from 'react';
import { useDispatch } from 'react-redux';
import { addTodo } from '../features/todo/todoSlice'; //import the action to add todo
export default function AddForm(){
    const [task,setTask] = useState("");
    const dispatch = useDispatch(); //to dispatch the action to the store
    //we can use the dispatch to dispatch the action to the store
    const submitHandler = (e) => {
        e.preventDefault();
        console.log(task);
        //it already know the state theu not passign any state plus we just need to pass the payload data
        dispatch(addTodo(task)); //dispatch the action to the store
        setTask(""); //clear the input field after adding the todo

    }
    return (
        <>
            <h2>Add Todo</h2>
            <form>
                <input type="text" placeholder="Enter todo" onChange={(e)=>setTask(e.target.value)} />
                <button type="submit">Add</button>
            </form>
        </>
    )
}