import { useSelector } from "react-redux";
import AddForm from "./AddForm"; //import the AddForm component
export default function Todo(){
    const todos=useSelector((state) => state.todos);  //access the store data!
    console.log(todos);
    return (
        <>
            <AddForm />
            <h2>Todos</h2>
            <ul >
                {
                    todos.map((todo)=><li key={todo.id}>{todo.task}</li>)
                }
            </ul>
        </>
    )
}