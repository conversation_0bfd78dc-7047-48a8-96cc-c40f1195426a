import { useState } from 'react'
import './App.css'
import Todo from './components/Todo'
import {Provider} from 'react-redux'
import {store} from './app/store' //import the store
import AddForm from './components/AddForm' //import the AddForm component
function App() {
  return (
    <>
      <Provider store={store}> {/*to give access of the store to every nested component */}
        {/* <AddForm /> */}
        <Todo/>

      </Provider>
    </>
  )
}

export default App
