//create a resuces for todoSlice which has  id ,task, and isDone as paramenters
import { createSlice,nanoid } from '@reduxjs/toolkit';

// actions structure is like this:
// {
//     type:"",
//     payload:""  //this can be object!
// }
// what is the structure of this payload?


//the state and section for each function is different for each function

const initialState = {
  todos: [{ id: nanoid(), task: "demo-task", isDone: false }],
};

const todoSlice = createSlice({
  name: 'todos',
  initialState,  //initial state of the todos
  reducers: {   //an object of the functions that will modify the state
    addTodo: (state, action) => {  //these are the actions
      state.todos.push({
        id: nanoid(),
        task: action.payload,
        isDone: false
    });  //direct mutation of the array
      //but in the react we cannot directly push in the state
      //we have to create a new array with the new todo item
      // return [...state.todos, { id: Date.now(), task: action.payload, isDone: false }];
      //but in redux toolkit we can directly mutate the state
    },
    markAsDone: (state, action) => {
      const todo = state.todos.find(todo => todo.id === action.payload);
      if (todo) {
        todo.isDone = !todo.isDone;
      }
    },
    deleteTodo: (state, action) => {
      return state.todos.filter(todo => todo.id !== action.payload);
    }
  }
});

export const { addTodo, markAsDone, deleteTodo } = todoSlice.actions;   //used to convert these actions into objects that can be dispatched
//and then we can use these actions in the components to dispatch the actions to the store
export default todoSlice.reducer;
